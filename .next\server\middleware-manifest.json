{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api\\/auth|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "n5+AQho2asUYJr9628VHY9alUJXBlOIZqk0uXYe/PPg=", "__NEXT_PREVIEW_MODE_ID": "42c14ab7f25b6e6fb319cd164ec47d0f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "785b05d2befc22c3f15403873571afd9812ca9d163c92b03c80d27437e9b96bc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0be4af647a04cf8001c9a936fbc653a186ff0b8486f57f716c4b80ec71e72ec4"}}}, "sortedMiddleware": ["/"], "functions": {}}