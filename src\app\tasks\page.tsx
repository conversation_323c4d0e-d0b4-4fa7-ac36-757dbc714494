'use client'

import { useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  CheckSquare2, 
  Plus, 
  Calendar,
  Clock,
  User,
  Flag,
  MoreHorizontal,
  Filter,
  Search
} from 'lucide-react'
import { Input } from '@/components/ui/input'

// Mock data for tasks
const mockTasks = [
  {
    id: '1',
    title: 'Design user authentication flow',
    description: 'Create wireframes and mockups for the login and registration process',
    status: 'In Progress',
    priority: 'High',
    dueDate: '2024-01-25',
    project: 'TaskPro Mobile App',
    assignee: { name: '<PERSON>', avatar: '' },
    createdAt: '2024-01-15'
  },
  {
    id: '2',
    title: 'Implement API endpoints',
    description: 'Build REST API endpoints for user management',
    status: 'Todo',
    priority: 'Medium',
    dueDate: '2024-01-30',
    project: 'API v2.0',
    assignee: { name: 'Sarah Wilson', avatar: '' },
    createdAt: '2024-01-16'
  },
  {
    id: '3',
    title: 'Write unit tests',
    description: 'Add comprehensive unit tests for the authentication module',
    status: 'Done',
    priority: 'Low',
    dueDate: '2024-01-20',
    project: 'API v2.0',
    assignee: { name: 'Tom Brown', avatar: '' },
    createdAt: '2024-01-10'
  },
  {
    id: '4',
    title: 'Update documentation',
    description: 'Update API documentation with new endpoints',
    status: 'In Review',
    priority: 'Medium',
    dueDate: '2024-01-28',
    project: 'Website Redesign',
    assignee: { name: 'Emily Davis', avatar: '' },
    createdAt: '2024-01-18'
  },
  {
    id: '5',
    title: 'Fix responsive layout issues',
    description: 'Resolve mobile layout problems on the dashboard',
    status: 'Todo',
    priority: 'Urgent',
    dueDate: '2024-01-22',
    project: 'Website Redesign',
    assignee: { name: 'Alex Chen', avatar: '' },
    createdAt: '2024-01-19'
  }
]

const getStatusColor = (status: string) => {
  switch (status) {
    case 'Todo':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    case 'In Progress':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
    case 'In Review':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
    case 'Done':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
  }
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'Low':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
    case 'Medium':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
    case 'High':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'
    case 'Urgent':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
  }
}

const isOverdue = (dueDate: string) => {
  return new Date(dueDate) < new Date() && new Date(dueDate).toDateString() !== new Date().toDateString()
}

export default function TasksPage() {
  const [tasks] = useState(mockTasks)
  const [searchQuery, setSearchQuery] = useState('')

  const filteredTasks = tasks.filter(task =>
    task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    task.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    task.project.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const getTasksByStatus = (status: string) => {
    return filteredTasks.filter(task => task.status === status)
  }

  const TaskCard = ({ task }: { task: typeof mockTasks[0] }) => (
    <Card className="card-hover border-primary/10">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1 flex-1">
            <CardTitle className="text-base leading-tight">{task.title}</CardTitle>
            <CardDescription className="text-sm">{task.description}</CardDescription>
          </div>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0 space-y-3">
        {/* Status and Priority */}
        <div className="flex items-center gap-2">
          <Badge className={getStatusColor(task.status)} variant="secondary">
            {task.status}
          </Badge>
          <Badge className={getPriorityColor(task.priority)} variant="secondary">
            <Flag className="h-3 w-3 mr-1" />
            {task.priority}
          </Badge>
        </div>

        {/* Project */}
        <div className="text-sm text-muted-foreground">
          📁 {task.project}
        </div>

        {/* Due Date and Assignee */}
        <div className="flex items-center justify-between text-sm">
          <div className={`flex items-center gap-1 ${isOverdue(task.dueDate) ? 'text-red-600 dark:text-red-400' : 'text-muted-foreground'}`}>
            <Calendar className="h-3 w-3" />
            <span>{new Date(task.dueDate).toLocaleDateString()}</span>
            {isOverdue(task.dueDate) && <span className="text-xs">(Overdue)</span>}
          </div>
          <div className="flex items-center gap-1">
            <User className="h-3 w-3 text-muted-foreground" />
            <Avatar className="h-6 w-6">
              <AvatarImage src={task.assignee.avatar} />
              <AvatarFallback className="text-xs">
                {task.assignee.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
          </div>
        </div>

        {/* Created Date */}
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <Clock className="h-3 w-3" />
          <span>Created {new Date(task.createdAt).toLocaleDateString()}</span>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <MainLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              <span className="gradient-text">My Tasks</span>
            </h1>
            <p className="text-muted-foreground">
              Manage and track your assigned tasks across all projects
            </p>
          </div>
          <Button className="btn-primary">
            <Plus className="mr-2 h-4 w-4" />
            New Task
          </Button>
        </div>

        {/* Search and Filters */}
        <div className="flex items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search tasks..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button variant="outline">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
        </div>

        {/* Tasks Tabs */}
        <Tabs defaultValue="all" className="space-y-4">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="all">All ({filteredTasks.length})</TabsTrigger>
            <TabsTrigger value="todo">Todo ({getTasksByStatus('Todo').length})</TabsTrigger>
            <TabsTrigger value="progress">In Progress ({getTasksByStatus('In Progress').length})</TabsTrigger>
            <TabsTrigger value="review">Review ({getTasksByStatus('In Review').length})</TabsTrigger>
            <TabsTrigger value="done">Done ({getTasksByStatus('Done').length})</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredTasks.map((task) => (
                <TaskCard key={task.id} task={task} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="todo" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {getTasksByStatus('Todo').map((task) => (
                <TaskCard key={task.id} task={task} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="progress" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {getTasksByStatus('In Progress').map((task) => (
                <TaskCard key={task.id} task={task} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="review" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {getTasksByStatus('In Review').map((task) => (
                <TaskCard key={task.id} task={task} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="done" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {getTasksByStatus('Done').map((task) => (
                <TaskCard key={task.id} task={task} />
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Empty State */}
        {filteredTasks.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <div className="mx-auto w-12 h-12 bg-gradient-to-br from-primary to-blue-500 rounded-lg flex items-center justify-center mb-4 shadow-lg shadow-primary/25">
                <CheckSquare2 className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold mb-2">No tasks found</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery ? 'Try adjusting your search terms' : 'You don\'t have any tasks assigned yet'}
              </p>
              {!searchQuery && (
                <Button className="btn-primary">
                  <Plus className="mr-2 h-4 w-4" />
                  Create Your First Task
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  )
}
