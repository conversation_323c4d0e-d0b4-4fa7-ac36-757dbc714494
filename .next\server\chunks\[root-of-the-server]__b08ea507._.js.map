{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/taskpro/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/taskpro/src/lib/db.ts"], "sourcesContent": ["import { prisma } from './prisma'\nimport { Role, Priority, TaskStatus } from '@prisma/client'\n\n// User operations\nexport const userOperations = {\n  async findByEmail(email: string) {\n    return prisma.user.findUnique({\n      where: { email },\n      include: {\n        teamMembers: {\n          include: {\n            team: true\n          }\n        }\n      }\n    })\n  },\n\n  async create(data: {\n    email: string\n    name?: string\n    password?: string\n    image?: string\n  }) {\n    return prisma.user.create({\n      data\n    })\n  },\n\n  async updateRole(userId: string, role: Role) {\n    return prisma.user.update({\n      where: { id: userId },\n      data: { role }\n    })\n  }\n}\n\n// Team operations\nexport const teamOperations = {\n  async create(data: {\n    name: string\n    description?: string\n    creatorId: string\n  }) {\n    return prisma.team.create({\n      data: {\n        name: data.name,\n        description: data.description,\n        members: {\n          create: {\n            userId: data.creatorId,\n            role: Role.ADMIN\n          }\n        }\n      },\n      include: {\n        members: {\n          include: {\n            user: true\n          }\n        }\n      }\n    })\n  },\n\n  async addMember(teamId: string, userId: string, role: Role = Role.MEMBER) {\n    return prisma.teamMember.create({\n      data: {\n        teamId,\n        userId,\n        role\n      }\n    })\n  },\n\n  async getTeamsByUser(userId: string) {\n    return prisma.team.findMany({\n      where: {\n        members: {\n          some: {\n            userId\n          }\n        }\n      },\n      include: {\n        members: {\n          include: {\n            user: true\n          }\n        },\n        projects: {\n          include: {\n            tasks: true\n          }\n        }\n      }\n    })\n  }\n}\n\n// Project operations\nexport const projectOperations = {\n  async create(data: {\n    name: string\n    description?: string\n    teamId: string\n  }) {\n    return prisma.project.create({\n      data,\n      include: {\n        team: true,\n        tasks: true\n      }\n    })\n  },\n\n  async getByTeam(teamId: string) {\n    return prisma.project.findMany({\n      where: { teamId },\n      include: {\n        tasks: {\n          include: {\n            assignee: true,\n            createdBy: true\n          }\n        }\n      }\n    })\n  }\n}\n\n// Task operations\nexport const taskOperations = {\n  async create(data: {\n    title: string\n    description?: string\n    projectId: string\n    createdById: string\n    assigneeId?: string\n    priority?: Priority\n    dueDate?: Date\n  }) {\n    return prisma.task.create({\n      data,\n      include: {\n        assignee: true,\n        createdBy: true,\n        project: true\n      }\n    })\n  },\n\n  async updateStatus(taskId: string, status: TaskStatus) {\n    return prisma.task.update({\n      where: { id: taskId },\n      data: { status },\n      include: {\n        assignee: true,\n        createdBy: true,\n        project: true\n      }\n    })\n  },\n\n  async getByProject(projectId: string) {\n    return prisma.task.findMany({\n      where: { projectId },\n      include: {\n        assignee: true,\n        createdBy: true,\n        comments: {\n          include: {\n            user: true\n          }\n        },\n        attachments: true\n      },\n      orderBy: {\n        createdAt: 'desc'\n      }\n    })\n  },\n\n  async getByUser(userId: string) {\n    return prisma.task.findMany({\n      where: {\n        OR: [\n          { assigneeId: userId },\n          { createdById: userId }\n        ]\n      },\n      include: {\n        assignee: true,\n        createdBy: true,\n        project: {\n          include: {\n            team: true\n          }\n        }\n      },\n      orderBy: {\n        createdAt: 'desc'\n      }\n    })\n  }\n}\n\n// Comment operations\nexport const commentOperations = {\n  async create(data: {\n    content: string\n    taskId: string\n    userId: string\n  }) {\n    return prisma.comment.create({\n      data,\n      include: {\n        user: true\n      }\n    })\n  },\n\n  async getByTask(taskId: string) {\n    return prisma.comment.findMany({\n      where: { taskId },\n      include: {\n        user: true\n      },\n      orderBy: {\n        createdAt: 'asc'\n      }\n    })\n  }\n}\n\n// Attachment operations\nexport const attachmentOperations = {\n  async create(data: {\n    filename: string\n    originalName: string\n    mimeType: string\n    size: number\n    url: string\n    taskId: string\n    userId: string\n  }) {\n    return prisma.attachment.create({\n      data,\n      include: {\n        user: true\n      }\n    })\n  },\n\n  async getByTask(taskId: string) {\n    return prisma.attachment.findMany({\n      where: { taskId },\n      include: {\n        user: true\n      },\n      orderBy: {\n        createdAt: 'desc'\n      }\n    })\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAGO,MAAM,iBAAiB;IAC5B,MAAM,aAAY,KAAa;QAC7B,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAC5B,OAAO;gBAAE;YAAM;YACf,SAAS;gBACP,aAAa;oBACX,SAAS;wBACP,MAAM;oBACR;gBACF;YACF;QACF;IACF;IAEA,MAAM,QAAO,IAKZ;QACC,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACxB;QACF;IACF;IAEA,MAAM,YAAW,MAAc,EAAE,IAAU;QACzC,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACxB,OAAO;gBAAE,IAAI;YAAO;YACpB,MAAM;gBAAE;YAAK;QACf;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM,QAAO,IAIZ;QACC,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACxB,MAAM;gBACJ,MAAM,KAAK,IAAI;gBACf,aAAa,KAAK,WAAW;gBAC7B,SAAS;oBACP,QAAQ;wBACN,QAAQ,KAAK,SAAS;wBACtB,MAAM,6HAAA,CAAA,OAAI,CAAC,KAAK;oBAClB;gBACF;YACF;YACA,SAAS;gBACP,SAAS;oBACP,SAAS;wBACP,MAAM;oBACR;gBACF;YACF;QACF;IACF;IAEA,MAAM,WAAU,MAAc,EAAE,MAAc,EAAE,OAAa,6HAAA,CAAA,OAAI,CAAC,MAAM;QACtE,OAAO,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YAC9B,MAAM;gBACJ;gBACA;gBACA;YACF;QACF;IACF;IAEA,MAAM,gBAAe,MAAc;QACjC,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC1B,OAAO;gBACL,SAAS;oBACP,MAAM;wBACJ;oBACF;gBACF;YACF;YACA,SAAS;gBACP,SAAS;oBACP,SAAS;wBACP,MAAM;oBACR;gBACF;gBACA,UAAU;oBACR,SAAS;wBACP,OAAO;oBACT;gBACF;YACF;QACF;IACF;AACF;AAGO,MAAM,oBAAoB;IAC/B,MAAM,QAAO,IAIZ;QACC,OAAO,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC3B;YACA,SAAS;gBACP,MAAM;gBACN,OAAO;YACT;QACF;IACF;IAEA,MAAM,WAAU,MAAc;QAC5B,OAAO,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC7B,OAAO;gBAAE;YAAO;YAChB,SAAS;gBACP,OAAO;oBACL,SAAS;wBACP,UAAU;wBACV,WAAW;oBACb;gBACF;YACF;QACF;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM,QAAO,IAQZ;QACC,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACxB;YACA,SAAS;gBACP,UAAU;gBACV,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,MAAM,cAAa,MAAc,EAAE,MAAkB;QACnD,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACxB,OAAO;gBAAE,IAAI;YAAO;YACpB,MAAM;gBAAE;YAAO;YACf,SAAS;gBACP,UAAU;gBACV,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,MAAM,cAAa,SAAiB;QAClC,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC1B,OAAO;gBAAE;YAAU;YACnB,SAAS;gBACP,UAAU;gBACV,WAAW;gBACX,UAAU;oBACR,SAAS;wBACP,MAAM;oBACR;gBACF;gBACA,aAAa;YACf;YACA,SAAS;gBACP,WAAW;YACb;QACF;IACF;IAEA,MAAM,WAAU,MAAc;QAC5B,OAAO,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC1B,OAAO;gBACL,IAAI;oBACF;wBAAE,YAAY;oBAAO;oBACrB;wBAAE,aAAa;oBAAO;iBACvB;YACH;YACA,SAAS;gBACP,UAAU;gBACV,WAAW;gBACX,SAAS;oBACP,SAAS;wBACP,MAAM;oBACR;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;QACF;IACF;AACF;AAGO,MAAM,oBAAoB;IAC/B,MAAM,QAAO,IAIZ;QACC,OAAO,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC3B;YACA,SAAS;gBACP,MAAM;YACR;QACF;IACF;IAEA,MAAM,WAAU,MAAc;QAC5B,OAAO,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC7B,OAAO;gBAAE;YAAO;YAChB,SAAS;gBACP,MAAM;YACR;YACA,SAAS;gBACP,WAAW;YACb;QACF;IACF;AACF;AAGO,MAAM,uBAAuB;IAClC,MAAM,QAAO,IAQZ;QACC,OAAO,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YAC9B;YACA,SAAS;gBACP,MAAM;YACR;QACF;IACF;IAEA,MAAM,WAAU,MAAc;QAC5B,OAAO,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YAChC,OAAO;gBAAE;YAAO;YAChB,SAAS;gBACP,MAAM;YACR;YACA,SAAS;gBACP,WAAW;YACb;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/taskpro/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport { PrismaAdapter } from '@auth/prisma-adapter'\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport GoogleProvider from 'next-auth/providers/google'\nimport GitHubProvider from 'next-auth/providers/github'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from './prisma'\nimport { userOperations } from './db'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma) as any,\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await userOperations.findByEmail(credentials.email)\n\n        if (!user || !user.password) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          image: user.image,\n          role: user.role\n        }\n      }\n    }),\n    // Only add OAuth providers if credentials are configured\n    ...(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET ? [\n      GoogleProvider({\n        clientId: process.env.GOOGLE_CLIENT_ID,\n        clientSecret: process.env.GOOGLE_CLIENT_SECRET,\n      })\n    ] : []),\n    ...(process.env.GITHUB_ID && process.env.GITHUB_SECRET ? [\n      GitHubProvider({\n        clientId: process.env.GITHUB_ID,\n        clientSecret: process.env.GITHUB_SECRET,\n      })\n    ] : [])\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n      }\n      return session\n    },\n    async signIn({ user, account, profile }) {\n      if (account?.provider === 'google' || account?.provider === 'github') {\n        try {\n          const existingUser = await userOperations.findByEmail(user.email!)\n          \n          if (!existingUser) {\n            await userOperations.create({\n              email: user.email!,\n              name: user.name || '',\n              image: user.image\n            })\n          }\n          \n          return true\n        } catch (error) {\n          console.error('Error during sign in:', error)\n          return false\n        }\n      }\n      \n      return true\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup'\n  }\n}\n\n// Helper function to hash passwords\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12)\n}\n\n// Helper function to verify passwords\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,kHAAA,CAAA,iBAAc,CAAC,WAAW,CAAC,YAAY,KAAK;gBAE/D,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;oBAC3B,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;QACA,yDAAyD;WACrD,QAAQ,GAAG,CAAC,gBAAgB,IAAI,QAAQ,GAAG,CAAC,oBAAoB,GAAG;YACrE,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;gBACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;gBACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;YAChD;SACD,GAAG,EAAE;WACF,QAAQ,GAAG,CAAC,SAAS,IAAI,QAAQ,GAAG,CAAC,aAAa,GAAG;YACvD,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;gBACb,UAAU,QAAQ,GAAG,CAAC,SAAS;gBAC/B,cAAc,QAAQ,GAAG,CAAC,aAAa;YACzC;SACD,GAAG,EAAE;KACP;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;QACA,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,IAAI,SAAS,aAAa,YAAY,SAAS,aAAa,UAAU;gBACpE,IAAI;oBACF,MAAM,eAAe,MAAM,kHAAA,CAAA,iBAAc,CAAC,WAAW,CAAC,KAAK,KAAK;oBAEhE,IAAI,CAAC,cAAc;wBACjB,MAAM,kHAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;4BAC1B,OAAO,KAAK,KAAK;4BACjB,MAAM,KAAK,IAAI,IAAI;4BACnB,OAAO,KAAK,KAAK;wBACnB;oBACF;oBAEA,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,yBAAyB;oBACvC,OAAO;gBACT;YACF;YAEA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;AACF;AAGO,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC", "debugId": null}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/taskpro/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth'\nimport { authOptions } from '@/lib/auth'\n\nconst handler = NextAuth(authOptions)\n\nexport { handler as GET, handler as POST }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,oHAAA,CAAA,cAAW", "debugId": null}}]}