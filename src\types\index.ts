import { User, Team, Project, Task, Comment, Attachment, Role, Priority, TaskStatus } from '@prisma/client'

// Extended types with relations
export type UserWithTeams = User & {
  teamMembers: (TeamMember & {
    team: Team
  })[]
}

export type TeamMember = {
  id: string
  userId: string
  teamId: string
  role: Role
  user: User
  team: Team
}

export type TeamWithMembers = Team & {
  members: (TeamMember & {
    user: User
  })[]
  projects?: ProjectWithTasks[]
}

export type ProjectWithTasks = Project & {
  tasks: TaskWithDetails[]
  team?: Team
}

export type TaskWithDetails = Task & {
  assignee?: User | null
  createdBy: User
  project?: Project
  comments?: CommentWithUser[]
  attachments?: AttachmentWithUser[]
}

export type CommentWithUser = Comment & {
  user: User
}

export type AttachmentWithUser = Attachment & {
  user: User
}

// Form types
export interface CreateTeamForm {
  name: string
  description?: string
}

export interface CreateProjectForm {
  name: string
  description?: string
  teamId: string
}

export interface CreateTaskForm {
  title: string
  description?: string
  projectId: string
  assigneeId?: string
  priority: Priority
  dueDate?: Date
}

export interface UpdateTaskForm {
  title?: string
  description?: string
  status?: TaskStatus
  priority?: Priority
  assigneeId?: string
  dueDate?: Date
}

export interface CreateCommentForm {
  content: string
  taskId: string
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Socket.IO event types
export interface SocketEvents {
  'task:created': TaskWithDetails
  'task:updated': TaskWithDetails
  'task:deleted': { taskId: string }
  'comment:created': CommentWithUser
  'user:joined': { userId: string; teamId: string }
  'user:left': { userId: string; teamId: string }
}

// Notification types
export interface Notification {
  id: string
  type: 'task_assigned' | 'task_updated' | 'comment_added' | 'due_date_reminder'
  title: string
  message: string
  read: boolean
  createdAt: Date
  userId: string
  relatedId?: string // taskId, projectId, etc.
}

// Filter and sort types
export interface TaskFilters {
  status?: TaskStatus[]
  priority?: Priority[]
  assigneeId?: string[]
  projectId?: string[]
  dueDateFrom?: Date
  dueDateTo?: Date
}

export interface TaskSort {
  field: 'createdAt' | 'updatedAt' | 'dueDate' | 'priority' | 'title'
  direction: 'asc' | 'desc'
}

// Calendar types
export interface CalendarEvent {
  id: string
  title: string
  start: Date
  end?: Date
  allDay?: boolean
  color?: string
  task?: TaskWithDetails
}

// Dashboard types
export interface DashboardStats {
  totalTasks: number
  completedTasks: number
  overdueTasks: number
  tasksThisWeek: number
  teamMembers: number
  activeProjects: number
}

// Export all Prisma types
export {
  User,
  Team,
  Project,
  Task,
  Comment,
  Attachment,
  Role,
  Priority,
  TaskStatus
}
