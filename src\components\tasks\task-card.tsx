'use client'

import { useState } from 'react'
import { format } from 'date-fns'
import { Priority, TaskStatus } from '@prisma/client'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Calendar,
  Clock,
  MoreHorizontal,
  Edit,
  Trash2,
  User,
  MessageSquare,
  Paperclip
} from 'lucide-react'
import { TaskWithDetails } from '@/types'
import { cn } from '@/lib/utils'

interface TaskCardProps {
  task: TaskWithDetails
  onEdit?: (task: TaskWithDetails) => void
  onDelete?: (taskId: string) => void
  onStatusChange?: (taskId: string, status: TaskStatus) => void
  className?: string
}

const priorityColors = {
  [Priority.LOW]: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  [Priority.MEDIUM]: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  [Priority.HIGH]: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
  [Priority.URGENT]: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
}

const statusColors = {
  [TaskStatus.TODO]: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
  [TaskStatus.IN_PROGRESS]: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  [TaskStatus.IN_REVIEW]: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
  [TaskStatus.DONE]: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
}

export function TaskCard({
  task,
  onEdit,
  onDelete,
  onStatusChange,
  className
}: TaskCardProps) {
  const [isLoading, setIsLoading] = useState(false)

  const handleStatusChange = async (status: TaskStatus) => {
    if (!onStatusChange) return
    
    setIsLoading(true)
    try {
      await onStatusChange(task.id, status)
    } catch (error) {
      console.error('Error updating task status:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const isOverdue = task.dueDate && new Date(task.dueDate) < new Date() && task.status !== TaskStatus.DONE

  return (
    <Card className={cn(
      'group hover:shadow-md transition-shadow cursor-pointer',
      isOverdue && 'border-red-200 dark:border-red-800',
      className
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1 flex-1">
            <h3 className="font-semibold leading-none tracking-tight">
              {task.title}
            </h3>
            {task.description && (
              <p className="text-sm text-muted-foreground line-clamp-2">
                {task.description}
              </p>
            )}
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(task)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
              )}
              {onStatusChange && (
                <>
                  <DropdownMenuItem onClick={() => handleStatusChange(TaskStatus.TODO)}>
                    To Do
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleStatusChange(TaskStatus.IN_PROGRESS)}>
                    In Progress
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleStatusChange(TaskStatus.IN_REVIEW)}>
                    In Review
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleStatusChange(TaskStatus.DONE)}>
                    Done
                  </DropdownMenuItem>
                </>
              )}
              {onDelete && (
                <DropdownMenuItem
                  onClick={() => onDelete(task.id)}
                  className="text-red-600 dark:text-red-400"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Badge className={priorityColors[task.priority]}>
              {task.priority.toLowerCase()}
            </Badge>
            <Badge className={statusColors[task.status]}>
              {task.status.replace('_', ' ').toLowerCase()}
            </Badge>
          </div>
          
          {task.assignee && (
            <div className="flex items-center space-x-1">
              <User className="h-3 w-3 text-muted-foreground" />
              <Avatar className="h-6 w-6">
                <AvatarImage src={task.assignee.image || ''} />
                <AvatarFallback className="text-xs">
                  {task.assignee.name?.charAt(0)?.toUpperCase() || 'U'}
                </AvatarFallback>
              </Avatar>
            </div>
          )}
        </div>
        
        <div className="mt-3 flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center space-x-3">
            {task.dueDate && (
              <div className={cn(
                "flex items-center space-x-1",
                isOverdue && "text-red-600 dark:text-red-400"
              )}>
                <Calendar className="h-3 w-3" />
                <span>{format(new Date(task.dueDate), 'MMM dd')}</span>
              </div>
            )}
            
            <div className="flex items-center space-x-1">
              <Clock className="h-3 w-3" />
              <span>{format(new Date(task.createdAt), 'MMM dd')}</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {task.comments && task.comments.length > 0 && (
              <div className="flex items-center space-x-1">
                <MessageSquare className="h-3 w-3" />
                <span>{task.comments.length}</span>
              </div>
            )}
            
            {task.attachments && task.attachments.length > 0 && (
              <div className="flex items-center space-x-1">
                <Paperclip className="h-3 w-3" />
                <span>{task.attachments.length}</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
