import { prisma } from './prisma'
import { Role, Priority, TaskStatus } from '@prisma/client'

// User operations
export const userOperations = {
  async findByEmail(email: string) {
    return prisma.user.findUnique({
      where: { email },
      include: {
        teamMembers: {
          include: {
            team: true
          }
        }
      }
    })
  },

  async create(data: {
    email: string
    name?: string
    password?: string
    image?: string
  }) {
    return prisma.user.create({
      data
    })
  },

  async updateRole(userId: string, role: Role) {
    return prisma.user.update({
      where: { id: userId },
      data: { role }
    })
  }
}

// Team operations
export const teamOperations = {
  async create(data: {
    name: string
    description?: string
    creatorId: string
  }) {
    return prisma.team.create({
      data: {
        name: data.name,
        description: data.description,
        members: {
          create: {
            userId: data.creatorId,
            role: Role.ADMIN
          }
        }
      },
      include: {
        members: {
          include: {
            user: true
          }
        }
      }
    })
  },

  async addMember(teamId: string, userId: string, role: Role = Role.MEMBER) {
    return prisma.teamMember.create({
      data: {
        teamId,
        userId,
        role
      }
    })
  },

  async getTeamsByUser(userId: string) {
    return prisma.team.findMany({
      where: {
        members: {
          some: {
            userId
          }
        }
      },
      include: {
        members: {
          include: {
            user: true
          }
        },
        projects: {
          include: {
            tasks: true
          }
        }
      }
    })
  }
}

// Project operations
export const projectOperations = {
  async create(data: {
    name: string
    description?: string
    teamId: string
  }) {
    return prisma.project.create({
      data,
      include: {
        team: true,
        tasks: true
      }
    })
  },

  async getByTeam(teamId: string) {
    return prisma.project.findMany({
      where: { teamId },
      include: {
        tasks: {
          include: {
            assignee: true,
            createdBy: true
          }
        }
      }
    })
  }
}

// Task operations
export const taskOperations = {
  async create(data: {
    title: string
    description?: string
    projectId: string
    createdById: string
    assigneeId?: string
    priority?: Priority
    dueDate?: Date
  }) {
    return prisma.task.create({
      data,
      include: {
        assignee: true,
        createdBy: true,
        project: true
      }
    })
  },

  async updateStatus(taskId: string, status: TaskStatus) {
    return prisma.task.update({
      where: { id: taskId },
      data: { status },
      include: {
        assignee: true,
        createdBy: true,
        project: true
      }
    })
  },

  async getByProject(projectId: string) {
    return prisma.task.findMany({
      where: { projectId },
      include: {
        assignee: true,
        createdBy: true,
        comments: {
          include: {
            user: true
          }
        },
        attachments: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })
  },

  async getByUser(userId: string) {
    return prisma.task.findMany({
      where: {
        OR: [
          { assigneeId: userId },
          { createdById: userId }
        ]
      },
      include: {
        assignee: true,
        createdBy: true,
        project: {
          include: {
            team: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })
  }
}

// Comment operations
export const commentOperations = {
  async create(data: {
    content: string
    taskId: string
    userId: string
  }) {
    return prisma.comment.create({
      data,
      include: {
        user: true
      }
    })
  },

  async getByTask(taskId: string) {
    return prisma.comment.findMany({
      where: { taskId },
      include: {
        user: true
      },
      orderBy: {
        createdAt: 'asc'
      }
    })
  }
}

// Attachment operations
export const attachmentOperations = {
  async create(data: {
    filename: string
    originalName: string
    mimeType: string
    size: number
    url: string
    taskId: string
    userId: string
  }) {
    return prisma.attachment.create({
      data,
      include: {
        user: true
      }
    })
  },

  async getByTask(taskId: string) {
    return prisma.attachment.findMany({
      where: { taskId },
      include: {
        user: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })
  }
}
