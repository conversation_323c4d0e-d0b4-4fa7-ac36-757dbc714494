{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api/auth|_next/static|_next/image|favicon.ico|public).*){(\\\\.json)}?", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "n5+AQho2asUYJr9628VHY9alUJXBlOIZqk0uXYe/PPg=", "__NEXT_PREVIEW_MODE_ID": "30028a226d5c319a8968b6973b34b1b0", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "06ef529ef6ad91a85bf503affc8c3ee9bb8a91e37642dd184201654c3d9902a3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "75c154b1077cb4b9dca618ae150e0965eec03e258aac0af782280b968079461d"}}}, "instrumentation": null, "functions": {}}