import { useSession } from 'next-auth/react'
import { Role } from '@prisma/client'

export function useAuth() {
  const { data: session, status } = useSession()
  
  const isLoading = status === 'loading'
  const isAuthenticated = !!session?.user
  const user = session?.user
  
  const hasRole = (role: Role) => {
    return user?.role === role
  }
  
  const isAdmin = () => hasRole(Role.ADMIN)
  const isMember = () => hasRole(Role.MEMBER)
  
  return {
    user,
    isLoading,
    isAuthenticated,
    hasRole,
    isAdmin,
    isMember,
    session
  }
}
