{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/taskpro/src/hooks/use-auth.ts"], "sourcesContent": ["import { useSession } from 'next-auth/react'\nimport { Role } from '@prisma/client'\n\nexport function useAuth() {\n  const { data: session, status } = useSession()\n  \n  const isLoading = status === 'loading'\n  const isAuthenticated = !!session?.user\n  const user = session?.user\n  \n  const hasRole = (role: Role) => {\n    return user?.role === role\n  }\n  \n  const isAdmin = () => hasRole(Role.ADMIN)\n  const isMember = () => hasRole(Role.MEMBER)\n  \n  return {\n    user,\n    isLoading,\n    isAuthenticated,\n    hasRole,\n    isAdmin,\n    isMember,\n    session\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAE3C,MAAM,YAAY,WAAW;IAC7B,MAAM,kBAAkB,CAAC,CAAC,SAAS;IACnC,MAAM,OAAO,SAAS;IAEtB,MAAM,UAAU,CAAC;QACf,OAAO,MAAM,SAAS;IACxB;IAEA,MAAM,UAAU,IAAM,QAAQ,6HAAA,CAAA,OAAI,CAAC,KAAK;IACxC,MAAM,WAAW,IAAM,QAAQ,6HAAA,CAAA,OAAI,CAAC,MAAM;IAE1C,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/taskpro/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/taskpro/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/taskpro/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/taskpro/src/components/ui/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport { Moon, Sun } from 'lucide-react'\nimport { useTheme } from 'next-themes'\n\nimport { Button } from '@/components/ui/button'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\n\nexport function ThemeToggle() {\n  const { setTheme } = useTheme()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme('light')}>\n          Light\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('dark')}>\n          Dark\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('system')}>\n          System\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AACA;AAPA;;;;;;AAcO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAE5B,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAU;;;;;;kCAGpD,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAS;;;;;;kCAGnD,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAW;;;;;;;;;;;;;;;;;;AAM7D", "debugId": null}}, {"offset": {"line": 511, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/taskpro/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 563, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/taskpro/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useAuth } from '@/hooks/use-auth'\nimport { signOut } from 'next-auth/react'\nimport { Button } from '@/components/ui/button'\nimport { ThemeToggle } from '@/components/ui/theme-toggle'\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { \n  Bell, \n  Settings, \n  User, \n  LogOut, \n  CheckSquare,\n  Menu\n} from 'lucide-react'\n\nexport function Header() {\n  const { user, isAuthenticated } = useAuth()\n\n  const handleSignOut = () => {\n    signOut({ callbackUrl: '/' })\n  }\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container flex h-14 items-center\">\n        <div className=\"mr-4 hidden md:flex\">\n          <Link href=\"/\" className=\"mr-6 flex items-center space-x-2\">\n            <CheckSquare className=\"h-6 w-6\" />\n            <span className=\"hidden font-bold sm:inline-block\">\n              TaskPro\n            </span>\n          </Link>\n          {isAuthenticated && (\n            <nav className=\"flex items-center space-x-6 text-sm font-medium\">\n              <Link\n                href=\"/dashboard\"\n                className=\"transition-colors hover:text-foreground/80 text-foreground/60\"\n              >\n                Dashboard\n              </Link>\n              <Link\n                href=\"/teams\"\n                className=\"transition-colors hover:text-foreground/80 text-foreground/60\"\n              >\n                Teams\n              </Link>\n              <Link\n                href=\"/projects\"\n                className=\"transition-colors hover:text-foreground/80 text-foreground/60\"\n              >\n                Projects\n              </Link>\n              <Link\n                href=\"/calendar\"\n                className=\"transition-colors hover:text-foreground/80 text-foreground/60\"\n              >\n                Calendar\n              </Link>\n            </nav>\n          )}\n        </div>\n        \n        <Button\n          variant=\"ghost\"\n          className=\"mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden\"\n        >\n          <Menu className=\"h-5 w-5\" />\n          <span className=\"sr-only\">Toggle Menu</span>\n        </Button>\n        \n        <div className=\"flex flex-1 items-center justify-between space-x-2 md:justify-end\">\n          <div className=\"w-full flex-1 md:w-auto md:flex-none\">\n            <Link href=\"/\" className=\"flex items-center space-x-2 md:hidden\">\n              <CheckSquare className=\"h-6 w-6\" />\n              <span className=\"font-bold\">TaskPro</span>\n            </Link>\n          </div>\n          \n          <nav className=\"flex items-center space-x-2\">\n            {isAuthenticated ? (\n              <>\n                <Button variant=\"ghost\" size=\"icon\">\n                  <Bell className=\"h-4 w-4\" />\n                  <span className=\"sr-only\">Notifications</span>\n                </Button>\n                \n                <ThemeToggle />\n                \n                <DropdownMenu>\n                  <DropdownMenuTrigger asChild>\n                    <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                      <Avatar className=\"h-8 w-8\">\n                        <AvatarImage src={user?.image || ''} alt={user?.name || ''} />\n                        <AvatarFallback>\n                          {user?.name?.charAt(0)?.toUpperCase() || 'U'}\n                        </AvatarFallback>\n                      </Avatar>\n                    </Button>\n                  </DropdownMenuTrigger>\n                  <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n                    <DropdownMenuLabel className=\"font-normal\">\n                      <div className=\"flex flex-col space-y-1\">\n                        <p className=\"text-sm font-medium leading-none\">{user?.name}</p>\n                        <p className=\"text-xs leading-none text-muted-foreground\">\n                          {user?.email}\n                        </p>\n                      </div>\n                    </DropdownMenuLabel>\n                    <DropdownMenuSeparator />\n                    <DropdownMenuItem asChild>\n                      <Link href=\"/profile\">\n                        <User className=\"mr-2 h-4 w-4\" />\n                        <span>Profile</span>\n                      </Link>\n                    </DropdownMenuItem>\n                    <DropdownMenuItem asChild>\n                      <Link href=\"/settings\">\n                        <Settings className=\"mr-2 h-4 w-4\" />\n                        <span>Settings</span>\n                      </Link>\n                    </DropdownMenuItem>\n                    <DropdownMenuSeparator />\n                    <DropdownMenuItem onClick={handleSignOut}>\n                      <LogOut className=\"mr-2 h-4 w-4\" />\n                      <span>Log out</span>\n                    </DropdownMenuItem>\n                  </DropdownMenuContent>\n                </DropdownMenu>\n              </>\n            ) : (\n              <>\n                <ThemeToggle />\n                <Button variant=\"ghost\" asChild>\n                  <Link href=\"/auth/signin\">Sign In</Link>\n                </Button>\n                <Button asChild>\n                  <Link href=\"/auth/signup\">Sign Up</Link>\n                </Button>\n              </>\n            )}\n          </nav>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAhBA;;;;;;;;;;AAyBO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAExC,MAAM,gBAAgB;QACpB,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;YAAE,aAAa;QAAI;IAC7B;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;oCAAK,WAAU;8CAAmC;;;;;;;;;;;;wBAIpD,iCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAOP,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,WAAU;;sCAEV,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;8BAG5B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;;;;;;sCAIhC,8OAAC;4BAAI,WAAU;sCACZ,gCACC;;kDACE,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;;0DAC3B,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;kDAG5B,8OAAC,2IAAA,CAAA,cAAW;;;;;kDAEZ,8OAAC,4IAAA,CAAA,eAAY;;0DACX,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAO;0DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,WAAU;8DAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;;0EAChB,8OAAC,kIAAA,CAAA,cAAW;gEAAC,KAAK,MAAM,SAAS;gEAAI,KAAK,MAAM,QAAQ;;;;;;0EACxD,8OAAC,kIAAA,CAAA,iBAAc;0EACZ,MAAM,MAAM,OAAO,IAAI,iBAAiB;;;;;;;;;;;;;;;;;;;;;;0DAKjD,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,WAAU;gDAAO,OAAM;gDAAM,UAAU;;kEAC1D,8OAAC,4IAAA,CAAA,oBAAiB;wDAAC,WAAU;kEAC3B,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAoC,MAAM;;;;;;8EACvD,8OAAC;oEAAE,WAAU;8EACV,MAAM;;;;;;;;;;;;;;;;;kEAIb,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kEACtB,8OAAC,4IAAA,CAAA,mBAAgB;wDAAC,OAAO;kEACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;;8EACT,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;8EAAK;;;;;;;;;;;;;;;;;kEAGV,8OAAC,4IAAA,CAAA,mBAAgB;wDAAC,OAAO;kEACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;;8EACT,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;8EAAK;;;;;;;;;;;;;;;;;kEAGV,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kEACtB,8OAAC,4IAAA,CAAA,mBAAgB;wDAAC,SAAS;;0EACzB,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;6DAMd;;kDACE,8OAAC,2IAAA,CAAA,cAAW;;;;;kDACZ,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,OAAO;kDAC7B,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAe;;;;;;;;;;;kDAE5B,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;kDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5C", "debugId": null}}, {"offset": {"line": 1028, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/taskpro/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport { Button } from '@/components/ui/button'\nimport { ScrollArea } from '@/components/ui/scroll-area'\nimport {\n  LayoutDashboard,\n  Users,\n  FolderOpen,\n  Calendar,\n  Settings,\n  Plus,\n  CheckSquare2\n} from 'lucide-react'\n\nconst sidebarNavItems = [\n  {\n    title: 'Dashboard',\n    href: '/dashboard',\n    icon: LayoutDashboard\n  },\n  {\n    title: 'Teams',\n    href: '/teams',\n    icon: Users\n  },\n  {\n    title: 'Projects',\n    href: '/projects',\n    icon: FolderOpen\n  },\n  {\n    title: 'My Tasks',\n    href: '/tasks',\n    icon: CheckSquare2\n  },\n  {\n    title: 'Calendar',\n    href: '/calendar',\n    icon: Calendar\n  },\n  {\n    title: 'Settings',\n    href: '/settings',\n    icon: Settings\n  }\n]\n\ninterface SidebarProps {\n  className?: string\n}\n\nexport function Sidebar({ className }: SidebarProps) {\n  const pathname = usePathname()\n\n  return (\n    <div className={cn('pb-12', className)}>\n      <div className=\"space-y-4 py-4\">\n        <div className=\"px-3 py-2\">\n          <div className=\"space-y-1\">\n            <Button\n              variant=\"default\"\n              className=\"w-full justify-start\"\n              asChild\n            >\n              <Link href=\"/tasks/new\">\n                <Plus className=\"mr-2 h-4 w-4\" />\n                New Task\n              </Link>\n            </Button>\n          </div>\n        </div>\n        \n        <div className=\"px-3 py-2\">\n          <h2 className=\"mb-2 px-4 text-lg font-semibold tracking-tight\">\n            Navigation\n          </h2>\n          <div className=\"space-y-1\">\n            {sidebarNavItems.map((item) => (\n              <Button\n                key={item.href}\n                variant={pathname === item.href ? 'secondary' : 'ghost'}\n                className=\"w-full justify-start\"\n                asChild\n              >\n                <Link href={item.href}>\n                  <item.icon className=\"mr-2 h-4 w-4\" />\n                  {item.title}\n                </Link>\n              </Button>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAiBA,MAAM,kBAAkB;IACtB;QACE,OAAO;QACP,MAAM;QACN,MAAM,4NAAA,CAAA,kBAAe;IACvB;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;IACb;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,kNAAA,CAAA,aAAU;IAClB;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,qNAAA,CAAA,eAAY;IACpB;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;IAChB;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;IAChB;CACD;AAMM,SAAS,QAAQ,EAAE,SAAS,EAAgB;IACjD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,SAAS;kBAC1B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,OAAO;sCAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;;kDACT,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;8BAOzC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAG/D,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,kIAAA,CAAA,SAAM;oCAEL,SAAS,aAAa,KAAK,IAAI,GAAG,cAAc;oCAChD,WAAU;oCACV,OAAO;8CAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,KAAK,IAAI;;0DACnB,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,KAAK,KAAK;;;;;;;mCAPR,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgB9B", "debugId": null}}, {"offset": {"line": 1197, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/taskpro/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { ReactNode } from 'react'\nimport { Header } from './header'\nimport { Sidebar } from './sidebar'\nimport { useAuth } from '@/hooks/use-auth'\n\ninterface MainLayoutProps {\n  children: ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  const { isAuthenticated } = useAuth()\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <Header />\n        <main className=\"container mx-auto py-6\">\n          {children}\n        </main>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Header />\n      <div className=\"container flex-1 items-start md:grid md:grid-cols-[220px_minmax(0,1fr)] md:gap-6 lg:grid-cols-[240px_minmax(0,1fr)] lg:gap-10\">\n        <aside className=\"fixed top-14 z-30 -ml-2 hidden h-[calc(100vh-3.5rem)] w-full shrink-0 md:sticky md:block\">\n          <Sidebar className=\"h-full\" />\n        </aside>\n        <main className=\"flex w-full flex-col overflow-hidden\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAWO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAElC,IAAI,CAAC,iBAAiB;QACpB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;8BACP,8OAAC;oBAAK,WAAU;8BACb;;;;;;;;;;;;IAIT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;0BACP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCACf,cAAA,8OAAC,uIAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;kCAErB,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 1287, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/taskpro/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1384, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/taskpro/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/hooks/use-auth'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { \n  CheckSquare2, \n  Clock, \n  Users, \n  FolderOpen, \n  Plus,\n  TrendingUp,\n  AlertCircle\n} from 'lucide-react'\n\nexport default function DashboardPage() {\n  const { user, isLoading } = useAuth()\n\n  if (isLoading) {\n    return (\n      <MainLayout>\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"></div>\n        </div>\n      </MainLayout>\n    )\n  }\n\n  return (\n    <MainLayout>\n      <div className=\"space-y-6 p-6\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold tracking-tight\">\n              Welcome back, {user?.name?.split(' ')[0] || 'User'}!\n            </h1>\n            <p className=\"text-muted-foreground\">\n              Here's what's happening with your projects today.\n            </p>\n          </div>\n          <Button>\n            <Plus className=\"mr-2 h-4 w-4\" />\n            New Task\n          </Button>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Tasks</CardTitle>\n              <CheckSquare2 className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">24</div>\n              <p className=\"text-xs text-muted-foreground\">\n                +2 from last week\n              </p>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">In Progress</CardTitle>\n              <Clock className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">8</div>\n              <p className=\"text-xs text-muted-foreground\">\n                +1 from yesterday\n              </p>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Team Members</CardTitle>\n              <Users className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">12</div>\n              <p className=\"text-xs text-muted-foreground\">\n                +2 new members\n              </p>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Active Projects</CardTitle>\n              <FolderOpen className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">4</div>\n              <p className=\"text-xs text-muted-foreground\">\n                +1 this month\n              </p>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Recent Activity & Quick Actions */}\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-7\">\n          <Card className=\"col-span-4\">\n            <CardHeader>\n              <CardTitle>Recent Activity</CardTitle>\n              <CardDescription>\n                Your latest task updates and team activities\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium\">Task \"Design Homepage\" completed</p>\n                    <p className=\"text-xs text-muted-foreground\">2 hours ago</p>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium\">New comment on \"API Integration\"</p>\n                    <p className=\"text-xs text-muted-foreground\">4 hours ago</p>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-2 h-2 bg-yellow-500 rounded-full\"></div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium\">Task \"User Testing\" is due tomorrow</p>\n                    <p className=\"text-xs text-muted-foreground\">6 hours ago</p>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium\">John joined the \"Mobile App\" project</p>\n                    <p className=\"text-xs text-muted-foreground\">1 day ago</p>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card className=\"col-span-3\">\n            <CardHeader>\n              <CardTitle>Quick Actions</CardTitle>\n              <CardDescription>\n                Common tasks and shortcuts\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <Button className=\"w-full justify-start\" variant=\"outline\">\n                <Plus className=\"mr-2 h-4 w-4\" />\n                Create New Task\n              </Button>\n              <Button className=\"w-full justify-start\" variant=\"outline\">\n                <Users className=\"mr-2 h-4 w-4\" />\n                Invite Team Member\n              </Button>\n              <Button className=\"w-full justify-start\" variant=\"outline\">\n                <FolderOpen className=\"mr-2 h-4 w-4\" />\n                Start New Project\n              </Button>\n              <Button className=\"w-full justify-start\" variant=\"outline\">\n                <TrendingUp className=\"mr-2 h-4 w-4\" />\n                View Analytics\n              </Button>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Upcoming Deadlines */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <AlertCircle className=\"mr-2 h-5 w-5\" />\n              Upcoming Deadlines\n            </CardTitle>\n            <CardDescription>\n              Tasks that need your attention soon\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between p-3 border rounded-lg\">\n                <div>\n                  <p className=\"font-medium\">User Testing Phase</p>\n                  <p className=\"text-sm text-muted-foreground\">Mobile App Project</p>\n                </div>\n                <div className=\"text-right\">\n                  <p className=\"text-sm font-medium text-yellow-600\">Due Tomorrow</p>\n                  <p className=\"text-xs text-muted-foreground\">High Priority</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center justify-between p-3 border rounded-lg\">\n                <div>\n                  <p className=\"font-medium\">API Documentation</p>\n                  <p className=\"text-sm text-muted-foreground\">Backend Project</p>\n                </div>\n                <div className=\"text-right\">\n                  <p className=\"text-sm font-medium\">Due in 3 days</p>\n                  <p className=\"text-xs text-muted-foreground\">Medium Priority</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center justify-between p-3 border rounded-lg\">\n                <div>\n                  <p className=\"font-medium\">Design Review</p>\n                  <p className=\"text-sm text-muted-foreground\">Website Redesign</p>\n                </div>\n                <div className=\"text-right\">\n                  <p className=\"text-sm font-medium\">Due in 5 days</p>\n                  <p className=\"text-xs text-muted-foreground\">Low Priority</p>\n                </div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </MainLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAgBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAElC,IAAI,WAAW;QACb,qBACE,8OAAC,8IAAA,CAAA,aAAU;sBACT,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC,8IAAA,CAAA,aAAU;kBACT,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;;wCAAoC;wCACjC,MAAM,MAAM,MAAM,IAAI,CAAC,EAAE,IAAI;wCAAO;;;;;;;8CAErD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,8OAAC,kIAAA,CAAA,SAAM;;8CACL,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;8BAMrC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8OAAC,qNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;8CAE1B,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;8BAQnD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAIjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAIjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAIjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOvD,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;4CAAuB,SAAQ;;8DAC/C,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;4CAAuB,SAAQ;;8DAC/C,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;4CAAuB,SAAQ;;8DAC/C,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGzC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;4CAAuB,SAAQ;;8DAC/C,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ/C,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAG1C,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAc;;;;;;kEAC3B,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;0DAE/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAsC;;;;;;kEACnD,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;kDAIjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAc;;;;;;kEAC3B,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;0DAE/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAsB;;;;;;kEACnC,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;kDAIjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAc;;;;;;kEAC3B,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;0DAE/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAsB;;;;;;kEACnC,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/D", "debugId": null}}]}