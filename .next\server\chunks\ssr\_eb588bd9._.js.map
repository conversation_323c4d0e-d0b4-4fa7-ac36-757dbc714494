{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/taskpro/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/taskpro/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/taskpro/src/components/layout/main-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MainLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call MainLayout() from the server but MainLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/main-layout.tsx <module evaluation>\",\n    \"MainLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,uEACA", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/taskpro/src/components/layout/main-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MainLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call MainLayout() from the server but MainLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/main-layout.tsx\",\n    \"MainLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,mDACA", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/taskpro/src/app/page.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { Button } from '@/components/ui/button'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport {\n  CheckSquare,\n  Users,\n  Calendar,\n  BarChart3,\n  Zap,\n  Shield,\n  Smartphone,\n  ArrowRight,\n  Star\n} from 'lucide-react'\n\nexport default function Home() {\n  return (\n    <MainLayout>\n      {/* Hero Section */}\n      <section className=\"relative py-20 lg:py-32\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center max-w-4xl mx-auto\">\n            <h1 className=\"text-4xl md:text-6xl font-bold tracking-tight mb-6\">\n              Streamline Your Team's\n              <span className=\"text-primary\"> Productivity</span>\n            </h1>\n            <p className=\"text-xl text-muted-foreground mb-8 max-w-2xl mx-auto\">\n              TaskPro is a modern, intuitive task management platform that empowers teams\n              to collaborate efficiently, meet deadlines, and achieve their goals.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button size=\"lg\" asChild>\n                <Link href=\"/auth/signup\">\n                  Get Started Free\n                  <ArrowRight className=\"ml-2 h-4 w-4\" />\n                </Link>\n              </Button>\n              <Button size=\"lg\" variant=\"outline\" asChild>\n                <Link href=\"/auth/signin\">Sign In</Link>\n              </Button>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-20 bg-muted/50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">\n              Everything you need to manage tasks\n            </h2>\n            <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto\">\n              Powerful features designed to help teams stay organized and productive\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            <div className=\"text-center p-6\">\n              <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <CheckSquare className=\"h-6 w-6 text-primary\" />\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">Kanban Boards</h3>\n              <p className=\"text-muted-foreground\">\n                Visualize your workflow with intuitive drag-and-drop Kanban boards\n              </p>\n            </div>\n\n            <div className=\"text-center p-6\">\n              <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <Users className=\"h-6 w-6 text-primary\" />\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">Team Collaboration</h3>\n              <p className=\"text-muted-foreground\">\n                Work together seamlessly with real-time updates and notifications\n              </p>\n            </div>\n\n            <div className=\"text-center p-6\">\n              <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <Calendar className=\"h-6 w-6 text-primary\" />\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">Calendar View</h3>\n              <p className=\"text-muted-foreground\">\n                Track deadlines and schedule tasks with integrated calendar views\n              </p>\n            </div>\n\n            <div className=\"text-center p-6\">\n              <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <BarChart3 className=\"h-6 w-6 text-primary\" />\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">Analytics</h3>\n              <p className=\"text-muted-foreground\">\n                Get insights into team performance and project progress\n              </p>\n            </div>\n\n            <div className=\"text-center p-6\">\n              <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <Zap className=\"h-6 w-6 text-primary\" />\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">Real-time Updates</h3>\n              <p className=\"text-muted-foreground\">\n                Stay synchronized with instant updates across all devices\n              </p>\n            </div>\n\n            <div className=\"text-center p-6\">\n              <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <Shield className=\"h-6 w-6 text-primary\" />\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">Secure & Private</h3>\n              <p className=\"text-muted-foreground\">\n                Enterprise-grade security with role-based access control\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center max-w-3xl mx-auto\">\n            <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">\n              Ready to boost your team's productivity?\n            </h2>\n            <p className=\"text-xl text-muted-foreground mb-8\">\n              Join thousands of teams already using TaskPro to streamline their workflow\n            </p>\n            <Button size=\"lg\" asChild>\n              <Link href=\"/auth/signup\">\n                Start Your Free Trial\n                <ArrowRight className=\"ml-2 h-4 w-4\" />\n              </Link>\n            </Button>\n          </div>\n        </div>\n      </section>\n    </MainLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAYe,SAAS;IACtB,qBACE,8OAAC,8IAAA,CAAA,aAAU;;0BAET,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAqD;kDAEjE,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAEjC,8OAAC;gCAAE,WAAU;0CAAuD;;;;;;0CAIpE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,OAAO;kDACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;;gDAAe;8DAExB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG1B,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,SAAQ;wCAAU,OAAO;kDACzC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,8OAAC;oCAAE,WAAU;8CAAkD;;;;;;;;;;;;sCAKjE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;sDAEzB,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAGlD,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,OAAO;0CACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;;wCAAe;sDAExB,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtC", "debugId": null}}]}