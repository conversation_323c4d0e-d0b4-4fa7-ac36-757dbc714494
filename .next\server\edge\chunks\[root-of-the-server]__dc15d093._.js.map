{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { withAuth } from 'next-auth/middleware'\nimport { NextResponse } from 'next/server'\n\nexport default withAuth(\n  function middleware(req) {\n    // Check if user is trying to access admin routes\n    if (req.nextUrl.pathname.startsWith('/admin')) {\n      if (req.nextauth.token?.role !== 'ADMIN') {\n        return NextResponse.redirect(new URL('/dashboard', req.url))\n      }\n    }\n    \n    return NextResponse.next()\n  },\n  {\n    callbacks: {\n      authorized: ({ token, req }) => {\n        // Allow access to auth pages without token\n        if (req.nextUrl.pathname.startsWith('/auth/')) {\n          return true\n        }\n        \n        // Allow access to public pages\n        if (req.nextUrl.pathname === '/' || \n            req.nextUrl.pathname.startsWith('/api/auth/') ||\n            req.nextUrl.pathname.startsWith('/_next/') ||\n            req.nextUrl.pathname.startsWith('/favicon.ico')) {\n          return true\n        }\n        \n        // Require token for protected routes\n        return !!token\n      }\n    }\n  }\n)\n\nexport const config = {\n  matcher: [\n    '/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)',\n  ]\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;uCAEe,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,EACpB,SAAS,WAAW,GAAG;IACrB,iDAAiD;IACjD,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW;QAC7C,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE,SAAS,SAAS;YACxC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,IAAI,GAAG;QAC5D;IACF;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B,GACA;IACE,WAAW;QACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE;YACzB,2CAA2C;YAC3C,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW;gBAC7C,OAAO;YACT;YAEA,+BAA+B;YAC/B,IAAI,IAAI,OAAO,CAAC,QAAQ,KAAK,OACzB,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,iBAChC,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,cAChC,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,iBAAiB;gBACnD,OAAO;YACT;YAEA,qCAAqC;YACrC,OAAO,CAAC,CAAC;QACX;IACF;AACF;AAGK,MAAM,SAAS;IACpB,SAAS;QACP;KACD;AACH"}}]}