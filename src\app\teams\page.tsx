'use client'

import { useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Users, 
  Plus, 
  Settings, 
  Crown,
  UserPlus,
  MoreHorizontal,
  FolderOpen
} from 'lucide-react'

// Mock data for teams
const mockTeams = [
  {
    id: '1',
    name: 'Frontend Development',
    description: 'Building amazing user interfaces',
    memberCount: 8,
    projectCount: 3,
    role: 'Admin',
    members: [
      { id: '1', name: '<PERSON>', avatar: '', role: 'Admin' },
      { id: '2', name: '<PERSON>', avatar: '', role: 'Member' },
      { id: '3', name: '<PERSON>', avatar: '', role: 'Member' },
    ]
  },
  {
    id: '2',
    name: 'Backend Development',
    description: 'Server-side architecture and APIs',
    memberCount: 6,
    projectCount: 2,
    role: 'Member',
    members: [
      { id: '4', name: '<PERSON>', avatar: '', role: 'Admin' },
      { id: '5', name: 'Tom <PERSON>', avatar: '', role: 'Member' },
    ]
  },
  {
    id: '3',
    name: 'Design Team',
    description: 'Creating beautiful and intuitive designs',
    memberCount: 4,
    projectCount: 5,
    role: 'Member',
    members: [
      { id: '6', name: 'Emily Davis', avatar: '', role: 'Admin' },
      { id: '7', name: 'Alex Chen', avatar: '', role: 'Member' },
    ]
  }
]

export default function TeamsPage() {
  const [teams] = useState(mockTeams)

  return (
    <MainLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              <span className="gradient-text">Teams</span>
            </h1>
            <p className="text-muted-foreground">
              Manage your teams and collaborate with your colleagues
            </p>
          </div>
          <Button className="btn-primary">
            <Plus className="mr-2 h-4 w-4" />
            Create Team
          </Button>
        </div>

        {/* Teams Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {teams.map((team) => (
            <Card key={team.id} className="card-hover border-primary/10">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <CardTitle className="flex items-center gap-2">
                      {team.name}
                      {team.role === 'Admin' && (
                        <Crown className="h-4 w-4 text-yellow-500" />
                      )}
                    </CardTitle>
                    <CardDescription>{team.description}</CardDescription>
                  </div>
                  <Button variant="ghost" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Stats */}
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span>{team.memberCount} members</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <FolderOpen className="h-4 w-4 text-muted-foreground" />
                    <span>{team.projectCount} projects</span>
                  </div>
                </div>

                {/* Members Preview */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Members</span>
                    <Badge variant="secondary">{team.role}</Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="flex -space-x-2">
                      {team.members.slice(0, 3).map((member) => (
                        <Avatar key={member.id} className="h-8 w-8 border-2 border-background">
                          <AvatarImage src={member.avatar} />
                          <AvatarFallback className="text-xs">
                            {member.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                      ))}
                      {team.memberCount > 3 && (
                        <div className="h-8 w-8 rounded-full bg-muted border-2 border-background flex items-center justify-center">
                          <span className="text-xs text-muted-foreground">
                            +{team.memberCount - 3}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2 pt-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <UserPlus className="mr-2 h-4 w-4" />
                    Invite
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    <Settings className="mr-2 h-4 w-4" />
                    Settings
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State for New Users */}
        {teams.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <div className="mx-auto w-12 h-12 bg-gradient-to-br from-primary to-blue-500 rounded-lg flex items-center justify-center mb-4 shadow-lg shadow-primary/25">
                <Users className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold mb-2">No teams yet</h3>
              <p className="text-muted-foreground mb-4">
                Create your first team to start collaborating with others
              </p>
              <Button className="btn-primary">
                <Plus className="mr-2 h-4 w-4" />
                Create Your First Team
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  )
}
