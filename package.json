{"name": "taskpro", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.11.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@types/bcryptjs": "^2.4.6", "@types/multer": "^2.0.0", "@types/sharp": "^0.31.1", "@types/socket.io": "^3.0.1", "@types/socket.io-client": "^1.4.36", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.22.0", "lucide-react": "^0.525.0", "multer": "^2.0.1", "next": "15.3.4", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "prisma": "^6.11.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.59.0", "sharp": "^0.34.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}