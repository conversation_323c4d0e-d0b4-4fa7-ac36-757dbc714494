'use client'

import { useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Calendar as CalendarIcon, 
  Plus, 
  ChevronLeft,
  ChevronRight,
  Clock,
  Flag,
  Users
} from 'lucide-react'

// Mock data for calendar events/tasks
const mockEvents = [
  {
    id: '1',
    title: 'Design Review Meeting',
    date: '2024-01-25',
    time: '10:00 AM',
    type: 'meeting',
    priority: 'High',
    project: 'Website Redesign'
  },
  {
    id: '2',
    title: 'API Implementation Due',
    date: '2024-01-26',
    time: '5:00 PM',
    type: 'deadline',
    priority: 'Urgent',
    project: 'API v2.0'
  },
  {
    id: '3',
    title: 'Team Standup',
    date: '2024-01-27',
    time: '9:00 AM',
    type: 'meeting',
    priority: 'Medium',
    project: 'General'
  },
  {
    id: '4',
    title: 'User Testing Session',
    date: '2024-01-28',
    time: '2:00 PM',
    type: 'task',
    priority: 'High',
    project: 'TaskPro Mobile App'
  },
  {
    id: '5',
    title: 'Sprint Planning',
    date: '2024-01-29',
    time: '11:00 AM',
    type: 'meeting',
    priority: 'Medium',
    project: 'General'
  }
]

const getEventTypeColor = (type: string) => {
  switch (type) {
    case 'meeting':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
    case 'deadline':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    case 'task':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
  }
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'Low':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
    case 'Medium':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
    case 'High':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'
    case 'Urgent':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
  }
}

export default function CalendarPage() {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [events] = useState(mockEvents)

  // Get current month and year
  const currentMonth = currentDate.toLocaleString('default', { month: 'long' })
  const currentYear = currentDate.getFullYear()

  // Get days in current month
  const daysInMonth = new Date(currentYear, currentDate.getMonth() + 1, 0).getDate()
  const firstDayOfMonth = new Date(currentYear, currentDate.getMonth(), 1).getDay()

  // Generate calendar days
  const calendarDays = []
  
  // Add empty cells for days before the first day of the month
  for (let i = 0; i < firstDayOfMonth; i++) {
    calendarDays.push(null)
  }
  
  // Add days of the month
  for (let day = 1; day <= daysInMonth; day++) {
    calendarDays.push(day)
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate)
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1)
    } else {
      newDate.setMonth(newDate.getMonth() + 1)
    }
    setCurrentDate(newDate)
  }

  const getEventsForDate = (day: number) => {
    const dateString = `${currentYear}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`
    return events.filter(event => event.date === dateString)
  }

  const isToday = (day: number) => {
    const today = new Date()
    return (
      day === today.getDate() &&
      currentDate.getMonth() === today.getMonth() &&
      currentYear === today.getFullYear()
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              <span className="gradient-text">Calendar</span>
            </h1>
            <p className="text-muted-foreground">
              View and manage your schedule, deadlines, and meetings
            </p>
          </div>
          <Button className="btn-primary">
            <Plus className="mr-2 h-4 w-4" />
            New Event
          </Button>
        </div>

        <div className="grid gap-6 lg:grid-cols-3">
          {/* Calendar */}
          <div className="lg:col-span-2">
            <Card className="card-hover">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <div className="p-1 rounded-lg bg-gradient-to-br from-primary to-blue-500 shadow-lg shadow-primary/25">
                      <CalendarIcon className="h-5 w-5 text-white" />
                    </div>
                    {currentMonth} {currentYear}
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="icon" onClick={() => navigateMonth('prev')}>
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon" onClick={() => navigateMonth('next')}>
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {/* Calendar Grid */}
                <div className="grid grid-cols-7 gap-1 mb-4">
                  {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                    <div key={day} className="p-2 text-center text-sm font-medium text-muted-foreground">
                      {day}
                    </div>
                  ))}
                </div>
                
                <div className="grid grid-cols-7 gap-1">
                  {calendarDays.map((day, index) => {
                    if (day === null) {
                      return <div key={index} className="p-2 h-20"></div>
                    }
                    
                    const dayEvents = getEventsForDate(day)
                    const today = isToday(day)
                    
                    return (
                      <div
                        key={day}
                        className={`p-2 h-20 border rounded-lg cursor-pointer hover:bg-muted/50 transition-colors ${
                          today ? 'bg-primary/10 border-primary' : 'border-border'
                        }`}
                      >
                        <div className={`text-sm font-medium mb-1 ${today ? 'text-primary' : ''}`}>
                          {day}
                        </div>
                        <div className="space-y-1">
                          {dayEvents.slice(0, 2).map((event) => (
                            <div
                              key={event.id}
                              className="text-xs p-1 rounded bg-primary/20 text-primary truncate"
                            >
                              {event.title}
                            </div>
                          ))}
                          {dayEvents.length > 2 && (
                            <div className="text-xs text-muted-foreground">
                              +{dayEvents.length - 2} more
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Upcoming Events */}
          <div className="space-y-6">
            <Card className="card-hover">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Upcoming Events
                </CardTitle>
                <CardDescription>
                  Your schedule for the next few days
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {events.slice(0, 5).map((event) => (
                  <div key={event.id} className="flex items-start gap-3 p-3 rounded-lg border">
                    <div className="text-center min-w-[3rem]">
                      <div className="text-sm font-medium">
                        {new Date(event.date).toLocaleDateString('en', { day: 'numeric' })}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {new Date(event.date).toLocaleDateString('en', { month: 'short' })}
                      </div>
                    </div>
                    <div className="flex-1 space-y-1">
                      <h4 className="font-medium text-sm">{event.title}</h4>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        {event.time}
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getEventTypeColor(event.type)} variant="secondary">
                          {event.type}
                        </Badge>
                        <Badge className={getPriorityColor(event.priority)} variant="secondary">
                          <Flag className="h-3 w-3 mr-1" />
                          {event.priority}
                        </Badge>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        📁 {event.project}
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card className="card-hover">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  This Week
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Total Events</span>
                  <span className="font-medium">{events.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Meetings</span>
                  <span className="font-medium">{events.filter(e => e.type === 'meeting').length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Deadlines</span>
                  <span className="font-medium">{events.filter(e => e.type === 'deadline').length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Tasks</span>
                  <span className="font-medium">{events.filter(e => e.type === 'task').length}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
